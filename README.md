# X-ZoneServers - Modern Homepage

A modern, SEO-optimized homepage for X-ZoneServers hosting company featuring responsive design, performance optimization, and accessibility compliance.

## Features

### 🎨 Modern Design
- Clean, contemporary UI/UX design
- Responsive layout for all devices
- Modern CSS with custom properties
- Smooth animations and transitions
- Professional color scheme and typography

### 🚀 Performance Optimized
- Service Worker for caching and offline functionality
- Lazy loading for images
- Optimized CSS and JavaScript
- HTTP/2 Server Push support
- Compressed assets and browser caching

### 📱 Mobile-First Responsive
- Mobile-first design approach
- Responsive navigation with hamburger menu
- Touch-friendly interactive elements
- Optimized for all screen sizes

### ♿ Accessibility (WCAG Compliant)
- Semantic HTML structure
- Proper ARIA labels and roles
- Keyboard navigation support
- High contrast mode support
- Screen reader friendly
- Focus management

### 🔍 SEO Optimized
- Comprehensive meta tags
- Structured data (JSON-LD)
- Open Graph and Twitter Card tags
- Proper heading hierarchy (H1-H6)
- Sitemap and robots.txt
- Canonical URLs and hreflang

### 🛡️ Security & Performance
- Security headers implementation
- Content Security Policy ready
- XSS and clickjacking protection
- Optimized caching strategies
- HTTPS redirect ready

## File Structure

```
x-zoneservers/
├── index.html              # Main homepage
├── manifest.json           # PWA manifest
├── sw.js                   # Service worker
├── sitemap.xml            # XML sitemap
├── robots.txt             # Robots.txt file
├── .htaccess              # Apache configuration
├── assets/
│   ├── css/
│   │   └── styles.css     # Main stylesheet
│   ├── js/
│   │   └── main.js        # Main JavaScript
│   ├── images/            # Image assets
│   └── fonts/             # Web fonts
└── README.md              # This file
```

## Required Assets

To complete the setup, you'll need to add the following image assets:

### Logos
- `assets/images/logo.svg` - Main logo (light version)
- `assets/images/logo-white.svg` - White logo for dark backgrounds

### Favicons and Icons
- `assets/favicon.ico` - Main favicon
- `assets/apple-touch-icon.png` - Apple touch icon (180x180)
- `assets/images/favicon-32x32.png` - 32x32 favicon
- `assets/images/favicon-16x16.png` - 16x16 favicon
- `assets/images/icon-192.png` - PWA icon (192x192)
- `assets/images/icon-512.png` - PWA icon (512x512)
- `assets/images/icon-180.png` - iOS icon (180x180)

### Content Images
- `assets/images/about-servers.jpg` - About section image (600x400)
- `assets/images/og-image.jpg` - Open Graph image (1200x630)
- `assets/images/twitter-card.jpg` - Twitter card image (1200x600)

### Screenshots (for PWA)
- `assets/images/screenshot-desktop.png` - Desktop screenshot (1280x720)
- `assets/images/screenshot-mobile.png` - Mobile screenshot (390x844)

### Fonts (Optional)
- `assets/fonts/inter-var.woff2` - Inter variable font

## Setup Instructions

1. **Upload Files**: Upload all files to your web server
2. **Add Images**: Add the required image assets listed above
3. **Configure Server**: Ensure your server supports the .htaccess rules (Apache) or configure equivalent rules for Nginx
4. **HTTPS Setup**: Uncomment HTTPS redirect rules in .htaccess if using SSL
5. **Test**: Verify all functionality works correctly

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- iOS Safari 12+
- Android Chrome 60+

## Performance Metrics

The website is optimized for:
- **Core Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Lighthouse Score**: 90+ in all categories
- **Page Speed**: < 3s load time on 3G networks
- **Accessibility**: WCAG 2.1 AA compliance

## Customization

### Colors
Update CSS custom properties in `assets/css/styles.css`:
```css
:root {
  --primary-color: #3b82f6;
  --secondary-color: #1e293b;
  /* ... other colors */
}
```

### Content
- Update company information in `index.html`
- Modify structured data in the JSON-LD script
- Update contact information and forms
- Customize service offerings and pricing

### Branding
- Replace logo files with your brand assets
- Update favicon and PWA icons
- Modify color scheme to match brand guidelines

## Development

For local development:
1. Use a local server (e.g., `python -m http.server` or Live Server extension)
2. Test responsive design using browser dev tools
3. Validate HTML, CSS, and accessibility
4. Test performance with Lighthouse

## Deployment

1. **Production Checklist**:
   - [ ] All images optimized and compressed
   - [ ] HTTPS configured
   - [ ] Security headers enabled
   - [ ] Caching configured
   - [ ] Service worker tested
   - [ ] Forms connected to backend
   - [ ] Analytics tracking added
   - [ ] SEO meta tags verified

2. **Testing**:
   - [ ] Cross-browser compatibility
   - [ ] Mobile responsiveness
   - [ ] Accessibility compliance
   - [ ] Performance metrics
   - [ ] SEO validation

## Support

For technical support or customization requests, contact the development team.

## License

This code is proprietary to X-ZoneServers. All rights reserved.
