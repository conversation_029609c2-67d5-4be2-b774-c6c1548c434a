<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - X-ZoneServers</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
</head>
<body>
    <div class="error-page">
        <div class="container">
            <div class="error-content">
                <h1 class="error-title">404</h1>
                <h2 class="error-subtitle">Page Not Found</h2>
                <p class="error-description">
                    Sorry, the page you're looking for doesn't exist or has been moved.
                </p>
                <div class="error-actions">
                    <a href="/" class="btn btn--primary">Go Home</a>
                    <a href="javascript:history.back()" class="btn btn--secondary">Go Back</a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
        }
        
        .error-content {
            text-align: center;
            max-width: 500px;
        }
        
        .error-title {
            font-size: 8rem;
            font-weight: 900;
            color: var(--primary-color);
            margin-bottom: var(--space-4);
            line-height: 1;
        }
        
        .error-subtitle {
            font-size: var(--text-3xl);
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: var(--space-4);
        }
        
        .error-description {
            font-size: var(--text-lg);
            color: var(--gray-600);
            margin-bottom: var(--space-8);
        }
        
        .error-actions {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        @media (max-width: 480px) {
            .error-title {
                font-size: 6rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</body>
</html>
