/* X-ZoneServers - Modern CSS Styles */

/* CSS Custom Properties (Variables) */
:root {
  /* Enhanced Color Palette */
  --primary-color: #4f46e5;
  --primary-dark: #3730a3;
  --primary-light: #6366f1;
  --primary-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #06b6d4 100%);
  --secondary-color: #0f172a;
  --secondary-light: #1e293b;
  --accent-color: #06b6d4;
  --accent-secondary: #8b5cf6;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;

  /* Neutral Colors with Better Contrast */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Glass Effect Colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Enhanced Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-colored: 0 10px 25px -5px rgba(79, 70, 229, 0.2), 0 8px 10px -6px rgba(79, 70, 229, 0.1);
  --shadow-glow: 0 0 20px rgba(79, 70, 229, 0.3);

  /* Glassmorphism Effects */
  --glass-backdrop: backdrop-filter: blur(16px) saturate(180%);
  --glass-bg-light: rgba(255, 255, 255, 0.25);
  --glass-bg-dark: rgba(15, 23, 42, 0.25);
  --glass-border-light: rgba(255, 255, 255, 0.18);
  --glass-border-dark: rgba(255, 255, 255, 0.125);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Container */
  --container-max-width: 1200px;
  --container-padding: var(--space-6);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--text-6xl);
  font-weight: 900;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
h2 {
  font-size: var(--text-4xl);
  font-weight: 800;
}
h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
}
h4 {
  font-size: var(--text-2xl);
  font-weight: 600;
}
h5 {
  font-size: var(--text-xl);
  font-weight: 600;
}
h6 {
  font-size: var(--text-lg);
  font-weight: 600;
}

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
  line-height: 1.7;
  font-weight: 400;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Utility Classes */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: 800;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.section-subtitle {
  font-size: var(--text-xl);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: 600;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn--primary {
  background: var(--primary-gradient);
  color: var(--white);
  box-shadow: var(--shadow-colored);
  position: relative;
  overflow: hidden;
}

.btn--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn--primary:hover::before {
  left: 100%;
}

.btn--primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-glow), var(--shadow-xl);
  color: var(--white);
}

.btn--secondary {
  background: var(--white);
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn--secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
  transform: translateY(-1px);
}

.btn--outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn--outline:hover {
  background: var(--primary-color);
  color: var(--white);
}

.btn--large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

.btn--full {
  width: 100%;
}

.btn__icon {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.btn:hover .btn__icon {
  transform: translateX(2px);
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  z-index: 1000;
  transition: all var(--transition-fast);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--container-padding);
}

.nav__brand img {
  height: 40px;
  width: auto;
}

.nav__menu {
  display: flex;
  list-style: none;
  gap: var(--space-8);
  margin: 0;
  padding: 0;
}

.nav__link {
  font-weight: 500;
  color: var(--gray-700);
  transition: color var(--transition-fast);
}

.nav__link:hover {
  color: var(--primary-color);
}

.nav__actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.nav__toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
}

.nav__toggle-line {
  width: 24px;
  height: 2px;
  background: var(--gray-700);
  transition: all var(--transition-fast);
}

/* Enhanced Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero__gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(79, 70, 229, 0.3) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(124, 58, 237, 0.3) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
}

.hero__background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

.hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-20) 0;
}

.hero__title {
  font-size: var(--text-6xl);
  font-weight: 900;
  margin-bottom: var(--space-6);
  line-height: 1.1;
  color: var(--white);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero__subtitle {
  font-size: var(--text-xl);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
  line-height: 1.6;
}

.hero__actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  margin-bottom: var(--space-16);
  flex-wrap: wrap;
}

.hero__stats {
  display: flex;
  justify-content: center;
  gap: var(--space-12);
  flex-wrap: wrap;
}

.stat {
  text-align: center;
}

.stat__number {
  font-size: var(--text-3xl);
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-1);
}

.stat__label {
  font-size: var(--text-sm);
  color: var(--gray-500);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Enhanced Services Section */
.services {
  padding: var(--space-24) 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.03) 0%, transparent 50%),
    var(--white);
  position: relative;
}

.services::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(79, 70, 229, 0.1) 1px, transparent 0);
  background-size: 20px 20px;
  opacity: 0.3;
  pointer-events: none;
}

.services__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.service-card {
  background: var(--glass-bg-light);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid var(--glass-border-light);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.5), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(79, 70, 229, 0.3);
  background: rgba(255, 255, 255, 0.35);
}

.service-card:hover::before {
  opacity: 1;
}

.service-card__icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  color: var(--white);
  box-shadow: var(--shadow-colored);
  position: relative;
  transition: all var(--transition-normal);
}

.service-card__icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.service-card:hover .service-card__icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-glow), var(--shadow-xl);
}

.service-card:hover .service-card__icon::before {
  opacity: 0.3;
}

.service-card__title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.service-card__description {
  color: var(--gray-600);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.service-card__features {
  list-style: none;
  text-align: left;
}

.service-card__features li {
  padding: var(--space-2) 0;
  color: var(--gray-600);
  position: relative;
  padding-left: var(--space-6);
}

.service-card__features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: bold;
}

/* Features Section */
.features {
  padding: var(--space-24) 0;
  background: var(--gray-50);
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-8);
}

.feature {
  text-align: center;
  padding: var(--space-6);
}

.feature__icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  color: var(--white);
}

.feature__title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--space-3);
  color: var(--gray-900);
}

.feature__description {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Pricing Section */
.pricing {
  padding: var(--space-24) 0;
  background: var(--white);
}

.pricing__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  position: relative;
  transition: all var(--transition-normal);
  overflow: hidden;
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-color);
}

.pricing-card:hover::before {
  transform: scaleX(1);
}

.pricing-card--featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--shadow-colored);
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(255, 255, 255, 1) 100%);
}

.pricing-card--featured::before {
  transform: scaleX(1);
}

.pricing-card--featured:hover {
  transform: scale(1.05) translateY(-8px);
  box-shadow: var(--shadow-glow), var(--shadow-2xl);
}

.pricing-card__badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-gradient);
  color: var(--white);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 700;
  box-shadow: var(--shadow-colored);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: translateX(-50%) scale(1); }
  50% { transform: translateX(-50%) scale(1.05); }
}

.pricing-card__header {
  margin-bottom: var(--space-8);
}

.pricing-card__title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.pricing-card__price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: var(--space-4);
}

.pricing-card__currency {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--gray-600);
}

.pricing-card__amount {
  font-size: var(--text-5xl);
  font-weight: 900;
  color: var(--primary-color);
  margin: 0 var(--space-1);
}

.pricing-card__period {
  font-size: var(--text-lg);
  color: var(--gray-500);
}

.pricing-card__description {
  color: var(--gray-600);
  margin-bottom: var(--space-6);
}

.pricing-card__features {
  list-style: none;
  text-align: left;
  margin-bottom: var(--space-8);
}

.pricing-card__features li {
  padding: var(--space-3) 0;
  color: var(--gray-600);
  position: relative;
  padding-left: var(--space-6);
  border-bottom: 1px solid var(--gray-100);
}

.pricing-card__features li:last-child {
  border-bottom: none;
}

.pricing-card__features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: bold;
}

/* About Section */
.about {
  padding: var(--space-24) 0;
  background: var(--gray-50);
}

.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.about__text {
  max-width: 500px;
}

.about__description {
  font-size: var(--text-lg);
  line-height: 1.7;
  margin-bottom: var(--space-6);
  color: var(--gray-600);
}

.about__stats {
  display: flex;
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.stat-item {
  text-align: center;
}

.stat-item__number {
  font-size: var(--text-3xl);
  font-weight: 900;
  color: var(--primary-color);
  margin-bottom: var(--space-1);
}

.stat-item__label {
  font-size: var(--text-sm);
  color: var(--gray-500);
  font-weight: 500;
}

.about__image {
  position: relative;
}

.about__image img {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  width: 100%;
  height: auto;
}

/* Contact Section */
.contact {
  padding: var(--space-24) 0;
  background: var(--white);
}

.contact__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  max-width: 1000px;
  margin: 0 auto;
}

.contact__title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-6);
  color: var(--gray-900);
}

.contact__item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.contact__icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.contact__label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.contact__value {
  font-size: var(--text-base);
  color: var(--gray-700);
  font-weight: 500;
}

.contact__value a {
  color: var(--primary-color);
  text-decoration: none;
}

.contact__value a:hover {
  text-decoration: underline;
}

/* Enhanced Form Styles */
.contact__form {
  background: var(--glass-bg-light);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  padding: var(--space-10);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--shadow-xl);
  position: relative;
}

.contact__form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-4) var(--space-5);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-xl);
  font-size: var(--text-base);
  font-family: inherit;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  transition: all var(--transition-normal);
  font-weight: 500;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1), var(--shadow-lg);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
  border-color: var(--gray-300);
  background: rgba(255, 255, 255, 0.9);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--space-16) 0 var(--space-8);
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-16);
  margin-bottom: var(--space-12);
}

.footer__brand {
  max-width: 300px;
}

.footer__brand img {
  margin-bottom: var(--space-4);
}

.footer__description {
  color: var(--gray-400);
  line-height: 1.6;
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.footer__title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--white);
  margin-bottom: var(--space-4);
}

.footer__list {
  list-style: none;
}

.footer__list li {
  margin-bottom: var(--space-2);
}

.footer__link {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer__link:hover {
  color: var(--white);
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-8);
  border-top: 1px solid var(--gray-800);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer__copyright {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.footer__security {
  display: flex;
  gap: var(--space-4);
}

.security-badge {
  background: var(--gray-800);
  color: var(--gray-300);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  border: 1px solid var(--gray-700);
}

/* Mobile Navigation Styles */
.nav__menu--open {
  display: flex !important;
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: var(--white);
  flex-direction: column;
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border-top: 1px solid var(--gray-200);
  z-index: 999;
}

.nav__toggle--active .nav__toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav__toggle--active .nav__toggle-line:nth-child(2) {
  opacity: 0;
}

.nav__toggle--active .nav__toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.nav-open {
  overflow: hidden;
}

/* Header scroll effects */
.header--scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 1024px) {
  :root {
    --container-padding: var(--space-4);
  }

  .hero__title {
    font-size: var(--text-5xl);
  }

  .section-title {
    font-size: var(--text-3xl);
  }

  .about__content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .contact__content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .footer__links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav__menu {
    display: none;
  }

  .nav__toggle {
    display: flex;
  }

  .hero__title {
    font-size: var(--text-4xl);
  }

  .hero__actions {
    flex-direction: column;
    align-items: center;
  }

  .hero__stats {
    gap: var(--space-8);
  }

  .services__grid {
    grid-template-columns: 1fr;
  }

  .features__grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing__grid {
    grid-template-columns: 1fr;
  }

  .pricing-card--featured {
    transform: none;
  }

  .pricing-card--featured:hover {
    transform: translateY(-4px);
  }

  .about__stats {
    justify-content: center;
  }

  .footer__links {
    grid-template-columns: 1fr;
  }

  .footer__bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: var(--space-3);
  }

  .hero {
    min-height: 80vh;
  }

  .hero__content {
    padding: var(--space-16) 0;
  }

  .hero__title {
    font-size: var(--text-3xl);
  }

  .hero__subtitle {
    font-size: var(--text-lg);
  }

  .btn--large {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }

  .features__grid {
    grid-template-columns: 1fr;
  }

  .service-card,
  .pricing-card {
    padding: var(--space-6);
  }

  .contact__form {
    padding: var(--space-6);
  }
}

/* Enhanced Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

/* Shimmer effect for loading states */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Focus styles for better accessibility */
.btn:focus,
.nav__link:focus,
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --gray-600: #000000;
    --gray-700: #000000;
    --gray-800: #000000;
    --gray-900: #000000;
  }
}

/* Enhanced Interactive Elements */
.interactive-element {
  transition: all var(--transition-normal);
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-2px);
}

/* Magnetic effect for buttons */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::after {
  width: 300px;
  height: 300px;
}

/* Floating animation for hero elements */
.float {
  animation: float 6s ease-in-out infinite;
}

.float:nth-child(2) {
  animation-delay: 2s;
}

.float:nth-child(3) {
  animation-delay: 4s;
}

/* Gradient text animation */
.gradient-text {
  background: var(--primary-gradient);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Glowing border effect */
.glow-border {
  position: relative;
  border-radius: var(--radius-2xl);
}

.glow-border::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.glow-border:hover::before {
  opacity: 1;
}
