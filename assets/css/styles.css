/* X-ZoneServers - Modern CSS Styles */

/* CSS Custom Properties (Variables) */
:root {
  /* Colors */
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #60a5fa;
  --secondary-color: #1e293b;
  --secondary-light: #334155;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Container */
  --container-max-width: 1200px;
  --container-padding: var(--space-6);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Utility Classes */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: 800;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.section-subtitle {
  font-size: var(--text-xl);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: 600;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn--primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.btn--secondary {
  background: var(--white);
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}

.btn--secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
  transform: translateY(-1px);
}

.btn--outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn--outline:hover {
  background: var(--primary-color);
  color: var(--white);
}

.btn--large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

.btn--full {
  width: 100%;
}

.btn__icon {
  width: 20px;
  height: 20px;
  transition: transform var(--transition-fast);
}

.btn:hover .btn__icon {
  transform: translateX(2px);
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  z-index: 1000;
  transition: all var(--transition-fast);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--container-padding);
}

.nav__brand img {
  height: 40px;
  width: auto;
}

.nav__menu {
  display: flex;
  list-style: none;
  gap: var(--space-8);
  margin: 0;
  padding: 0;
}

.nav__link {
  font-weight: 500;
  color: var(--gray-700);
  transition: color var(--transition-fast);
}

.nav__link:hover {
  color: var(--primary-color);
}

.nav__actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.nav__toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
}

.nav__toggle-line {
  width: 24px;
  height: 2px;
  background: var(--gray-700);
  transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero__gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at top, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
}

.hero__content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-20) 0;
}

.hero__title {
  font-size: var(--text-6xl);
  font-weight: 900;
  margin-bottom: var(--space-6);
  line-height: 1.1;
}

.hero__subtitle {
  font-size: var(--text-xl);
  color: var(--gray-600);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero__actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  margin-bottom: var(--space-16);
  flex-wrap: wrap;
}

.hero__stats {
  display: flex;
  justify-content: center;
  gap: var(--space-12);
  flex-wrap: wrap;
}

.stat {
  text-align: center;
}

.stat__number {
  font-size: var(--text-3xl);
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-1);
}

.stat__label {
  font-size: var(--text-sm);
  color: var(--gray-500);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Services Section */
.services {
  padding: var(--space-24) 0;
  background: var(--white);
}

.services__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.service-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.service-card__icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-xl);
  color: var(--white);
}

.service-card__title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.service-card__description {
  color: var(--gray-600);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.service-card__features {
  list-style: none;
  text-align: left;
}

.service-card__features li {
  padding: var(--space-2) 0;
  color: var(--gray-600);
  position: relative;
  padding-left: var(--space-6);
}

.service-card__features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: bold;
}

/* Features Section */
.features {
  padding: var(--space-24) 0;
  background: var(--gray-50);
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-8);
}

.feature {
  text-align: center;
  padding: var(--space-6);
}

.feature__icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-4);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  color: var(--white);
}

.feature__title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--space-3);
  color: var(--gray-900);
}

.feature__description {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Pricing Section */
.pricing {
  padding: var(--space-24) 0;
  background: var(--white);
}

.pricing__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  position: relative;
  transition: all var(--transition-normal);
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.pricing-card--featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.pricing-card--featured:hover {
  transform: scale(1.05) translateY(-4px);
}

.pricing-card__badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 600;
}

.pricing-card__header {
  margin-bottom: var(--space-8);
}

.pricing-card__title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

.pricing-card__price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: var(--space-4);
}

.pricing-card__currency {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--gray-600);
}

.pricing-card__amount {
  font-size: var(--text-5xl);
  font-weight: 900;
  color: var(--primary-color);
  margin: 0 var(--space-1);
}

.pricing-card__period {
  font-size: var(--text-lg);
  color: var(--gray-500);
}

.pricing-card__description {
  color: var(--gray-600);
  margin-bottom: var(--space-6);
}

.pricing-card__features {
  list-style: none;
  text-align: left;
  margin-bottom: var(--space-8);
}

.pricing-card__features li {
  padding: var(--space-3) 0;
  color: var(--gray-600);
  position: relative;
  padding-left: var(--space-6);
  border-bottom: 1px solid var(--gray-100);
}

.pricing-card__features li:last-child {
  border-bottom: none;
}

.pricing-card__features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: bold;
}

/* About Section */
.about {
  padding: var(--space-24) 0;
  background: var(--gray-50);
}

.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.about__text {
  max-width: 500px;
}

.about__description {
  font-size: var(--text-lg);
  line-height: 1.7;
  margin-bottom: var(--space-6);
  color: var(--gray-600);
}

.about__stats {
  display: flex;
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.stat-item {
  text-align: center;
}

.stat-item__number {
  font-size: var(--text-3xl);
  font-weight: 900;
  color: var(--primary-color);
  margin-bottom: var(--space-1);
}

.stat-item__label {
  font-size: var(--text-sm);
  color: var(--gray-500);
  font-weight: 500;
}

.about__image {
  position: relative;
}

.about__image img {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  width: 100%;
  height: auto;
}

/* Contact Section */
.contact {
  padding: var(--space-24) 0;
  background: var(--white);
}

.contact__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  max-width: 1000px;
  margin: 0 auto;
}

.contact__title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-6);
  color: var(--gray-900);
}

.contact__item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.contact__icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.contact__label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.contact__value {
  font-size: var(--text-base);
  color: var(--gray-700);
  font-weight: 500;
}

.contact__value a {
  color: var(--primary-color);
  text-decoration: none;
}

.contact__value a:hover {
  text-decoration: underline;
}

/* Form Styles */
.contact__form {
  background: var(--gray-50);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--gray-200);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-family: inherit;
  background: var(--white);
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--space-16) 0 var(--space-8);
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-16);
  margin-bottom: var(--space-12);
}

.footer__brand {
  max-width: 300px;
}

.footer__brand img {
  margin-bottom: var(--space-4);
}

.footer__description {
  color: var(--gray-400);
  line-height: 1.6;
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.footer__title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--white);
  margin-bottom: var(--space-4);
}

.footer__list {
  list-style: none;
}

.footer__list li {
  margin-bottom: var(--space-2);
}

.footer__link {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer__link:hover {
  color: var(--white);
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-8);
  border-top: 1px solid var(--gray-800);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer__copyright {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.footer__security {
  display: flex;
  gap: var(--space-4);
}

.security-badge {
  background: var(--gray-800);
  color: var(--gray-300);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  border: 1px solid var(--gray-700);
}

/* Mobile Navigation Styles */
.nav__menu--open {
  display: flex !important;
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: var(--white);
  flex-direction: column;
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  border-top: 1px solid var(--gray-200);
  z-index: 999;
}

.nav__toggle--active .nav__toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav__toggle--active .nav__toggle-line:nth-child(2) {
  opacity: 0;
}

.nav__toggle--active .nav__toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.nav-open {
  overflow: hidden;
}

/* Header scroll effects */
.header--scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 1024px) {
  :root {
    --container-padding: var(--space-4);
  }

  .hero__title {
    font-size: var(--text-5xl);
  }

  .section-title {
    font-size: var(--text-3xl);
  }

  .about__content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .contact__content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .footer__links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav__menu {
    display: none;
  }

  .nav__toggle {
    display: flex;
  }

  .hero__title {
    font-size: var(--text-4xl);
  }

  .hero__actions {
    flex-direction: column;
    align-items: center;
  }

  .hero__stats {
    gap: var(--space-8);
  }

  .services__grid {
    grid-template-columns: 1fr;
  }

  .features__grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing__grid {
    grid-template-columns: 1fr;
  }

  .pricing-card--featured {
    transform: none;
  }

  .pricing-card--featured:hover {
    transform: translateY(-4px);
  }

  .about__stats {
    justify-content: center;
  }

  .footer__links {
    grid-template-columns: 1fr;
  }

  .footer__bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: var(--space-3);
  }

  .hero {
    min-height: 80vh;
  }

  .hero__content {
    padding: var(--space-16) 0;
  }

  .hero__title {
    font-size: var(--text-3xl);
  }

  .hero__subtitle {
    font-size: var(--text-lg);
  }

  .btn--large {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }

  .features__grid {
    grid-template-columns: 1fr;
  }

  .service-card,
  .pricing-card {
    padding: var(--space-6);
  }

  .contact__form {
    padding: var(--space-6);
  }
}

/* Animation Classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Focus styles for better accessibility */
.btn:focus,
.nav__link:focus,
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --gray-600: #000000;
    --gray-700: #000000;
    --gray-800: #000000;
    --gray-900: #000000;
  }
}
